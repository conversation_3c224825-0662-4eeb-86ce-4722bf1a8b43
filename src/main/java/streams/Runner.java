package streams;

import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.util.List;

public class Runner {
    public static void main(String[] args) {

        List<WebElement> tables = driver.findElements(By.tagName("table"));

        tables.stream()
                .map(table -> table.findElements(By.tagName("tr")))
                .flatMap(List::stream)
                .toList();
    }
}
